// "//-----------------------------------------------------------------------".
// <copyright file="StopRequestedAquilaChannelRequestChangeStrategyTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.Tests.Aquila.Strategies
{
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Aquila.Strategies;
    using NBA.NextGen.VideoPlatform.Shared.Application.Aquila.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using Xunit;

    /// <summary>
    /// The StopRequestedAquilaChannelRequestChangeStrategyTests.
    /// </summary>
    public class StopRequestedAquilaChannelRequestChangeStrategyTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The aquila client service.
        /// </summary>
        private readonly Mock<IAquilaClientService> mockAquilaClientService;

        /// <summary>
        /// The mock telemetry service.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<StopRequestedAquilaChannelRequestChangeStrategy>> mockLogger;

        /// <summary>
        /// The StopRequestedAquilaChannelRequestChangeStrategy.
        /// </summary>
        private readonly StopRequestedAquilaChannelRequestChangeStrategy stopRequestedAquilaChannelRequestChangeStrategy;

        /// <summary>
        /// Initializes a new instance of the <see cref="StopRequestedAquilaChannelRequestChangeStrategyTests" /> class.
        /// </summary>
        public StopRequestedAquilaChannelRequestChangeStrategyTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Strict);
            this.mockAquilaClientService = this.mockRepository.Create<IAquilaClientService>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
            this.mockLogger = this.mockRepository.Create<ILogger<StopRequestedAquilaChannelRequestChangeStrategy>>();
            this.stopRequestedAquilaChannelRequestChangeStrategy = new StopRequestedAquilaChannelRequestChangeStrategy(this.mockAquilaClientService.Object, this.mockTelemetryService.Object, this.mockLogger.Object);
        }

        /// <summary>
        /// RequestChangeAsync with empty implementation case does nothing.
        /// </summary>
        /// <param name="aquilaChannelState">State of the aquila channel.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData(AquilaChannelState.Started)]
        [InlineData(AquilaChannelState.Starting)]
        [InlineData(AquilaChannelState.Deleted)]
        [InlineData(AquilaChannelState.CreationError)]
        public async Task RequestChangeAsync_WithEmptyImplementationCase_DoesNothingAsync(AquilaChannelState aquilaChannelState)
        {
            // Arrange
            this.SetupLoggerMock();
            if (aquilaChannelState == AquilaChannelState.CreationError)
            {
                this.mockTelemetryService.Setup(x => x.TrackEvent(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()));
            }

            // Act
            await this.stopRequestedAquilaChannelRequestChangeStrategy.RequestChangeAsync(aquilaChannelState, null, "ChannelId", "EventId").ConfigureAwait(false);

            // Assert
            this.mockRepository.VerifyAll();
        }

        /// <summary>
        /// RequestChangeAsync with Stopped or Stopping AquilaChannelState stops the channel.
        /// </summary>
        /// <param name="aquilaChannelState">State of the aquila channel.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData(AquilaChannelState.Stopped)]
        [InlineData(AquilaChannelState.Stopping)]
        public async Task RequestChangeAsync_WithStoppedOrStoppingAquilaChannelState_StopsTheChannelAsync(AquilaChannelState aquilaChannelState)
        {
            // Arrange
            var channelId = "ChannelId";
            var instanceId = "InstanceId";
            this.SetupLoggerMock();
            this.mockAquilaClientService.Setup(x => x.StopChannelInstanceAsync(channelId, instanceId)).Returns(Task.CompletedTask);
            this.mockTelemetryService.Setup(x => x.TrackEvent(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()));

            // Act
            await this.stopRequestedAquilaChannelRequestChangeStrategy.RequestChangeAsync(aquilaChannelState, instanceId, channelId, "EventId").ConfigureAwait(false);

            // Assert
            this.mockAquilaClientService.Verify(x => x.StopChannelInstanceAsync(channelId, instanceId), Times.Once);
            this.mockRepository.VerifyAll();
        }

        /// <summary>
        /// RequestChangeAsync with not implemented AquilaChannelState case, throws NotSupportedException.
        /// </summary>
        /// <param name="aquilaChannelState">State of the aquila channel.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData(AquilaChannelState.InfraCreating)]
        [InlineData(AquilaChannelState.StartError)]
        [InlineData(AquilaChannelState.StartRequested)]
        [InlineData(AquilaChannelState.Configured)]
        [InlineData(AquilaChannelState.StartRequestError)]
        public async Task RequestChangeAsync_WithNotImplementedAquilaChannelStateCase_ThrowsNotSupportedExceptionAsync(AquilaChannelState aquilaChannelState)
        {
            // Arrange
            this.SetupLoggerMock();

            // Act and Assert
            await Assert.ThrowsAsync<NotSupportedException>(() => this.stopRequestedAquilaChannelRequestChangeStrategy.RequestChangeAsync(aquilaChannelState, null, "ChannelId", "EventId")).ConfigureAwait(true);
        }

        /// <summary>
        /// ChannelState property returns StopRequested.
        /// </summary>
        [Fact]
        public void ChannelState_ReturnsStopRequested()
        {
            // Act
            var result = this.stopRequestedAquilaChannelRequestChangeStrategy.ChannelState;

            // Assert
            Assert.Equal(AquilaChannelState.StopRequested, result);
        }

        /// <summary>
        /// Sets up the logger mock to accept any log calls.
        /// </summary>
        private void SetupLoggerMock()
        {
            this.mockLogger.Setup(x => x.Log(
                It.IsAny<LogLevel>(),
                It.IsAny<EventId>(),
                It.IsAny<It.IsAnyType>(),
                It.IsAny<System.Exception>(),
                It.IsAny<System.Func<It.IsAnyType, System.Exception, string>>()));
        }
    }
}
