// "//-----------------------------------------------------------------------".
// <copyright file="ErrorActivatingAquilaChannelRequestChangeStrategy.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.Aquila.Strategies
{
    using System;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Interfaces.Aquila;
    using NBA.NextGen.VideoPlatform.Shared.Application.Aquila.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;

    /// <inheritdoc/>
    public class StopRequestedAquilaChannelRequestChangeStrategy : IAquilaRequestChangeStrategy
    {
        /// <summary>
        /// The media service.
        /// </summary>
        private readonly IAquilaClientService aquilaClientService;

        /// <summary>
        /// The telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<StopRequestedAquilaChannelRequestChangeStrategy> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="StopRequestedAquilaChannelRequestChangeStrategy"/> class.
        /// </summary>
        /// <param name="aquilaClientService">The media service.</param>
        /// <param name="telemetryService"> The telemetry service.</param>
        /// <param name="logger">The logger.</param>
        public StopRequestedAquilaChannelRequestChangeStrategy(IAquilaClientService aquilaClientService, ITelemetryService telemetryService, ILogger<StopRequestedAquilaChannelRequestChangeStrategy> logger)
        {
            this.aquilaClientService = aquilaClientService;
            this.telemetryService = telemetryService;
            this.logger = logger;
        }

        /// <inheritdoc/>
        public AquilaChannelState ChannelState => AquilaChannelState.StopRequested;

        /// <inheritdoc/>
        public async Task RequestChangeAsync(AquilaChannelState desiredAquilaChannelState, string instanceId, string channelId, string eventId)
        {
            switch (desiredAquilaChannelState)
            {
                case AquilaChannelState.Started:
                    // nothing
                    break;
                case AquilaChannelState.Starting:
                    // nothing
                    break;
                case AquilaChannelState.Stopping:
                    this.telemetryService.TrackEvent(eventId, EventTypes.AquilaActorChannelStateToStopping, EventData.CorrelationTag);
                    break;
                case AquilaChannelState.Stopped:
                    await this.aquilaClientService.StopChannelInstanceAsync(channelId, instanceId).ConfigureAwait(false);
                    this.logger.LogInformation("StopRequestedAquilaChannelRequestChangeStrategy: Stopped case hit for channel {ChannelId}", channelId);
                    this.telemetryService.TrackEvent(eventId, EventTypes.AquilaActorChannelStateToStopped, EventData.CorrelationTag);
                    break;
                case AquilaChannelState.Deleted:
                    // this.logger.LogInformation("StopRequestedAquilaChannelRequestChangeStrategy: Starting deletion for channel {ChannelId}", channelId);
                    // this.aquilaClientService.DeleteChannelAsync(channelId).ConfigureAwait(false);
                    this.logger.LogInformation("StopRequestedAquilaChannelRequestChangeStrategy: We shouldn't delete {ChannelId} until we get the STOPPING Aquila Channel State", channelId);
                    this.telemetryService.TrackEvent(eventId, EventTypes.AquilaActorChannelStateToDeleted, EventData.CorrelationTag);
                    break;
                case AquilaChannelState.CreationError:
                    this.telemetryService.TrackEvent(eventId, EventTypes.AquilaActorChannelStateToCreationError, EventData.CorrelationTag);
                    break;
                default:
                    throw new NotSupportedException();
            }
        }
    }
}