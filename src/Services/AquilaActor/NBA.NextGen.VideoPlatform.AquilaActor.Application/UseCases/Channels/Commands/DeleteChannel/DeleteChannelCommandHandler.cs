// "//-----------------------------------------------------------------------".
// <copyright file="DeleteChannelCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Commands.DeleteChannel
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Interfaces.Aquila;
    using NBA.NextGen.VideoPlatform.Shared.Application.Aquila.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformChannels.Entities;

    /// <summary>
    /// Delete Channel.
    /// </summary>
    public class DeleteChannelCommandHandler : IRequestHandler<DeleteChannelCommand, Unit>
    {
        /// <summary>
        /// The aquila request change strategy factory.
        /// </summary>
        private readonly IAquilaRequestChangeStrategyFactory aquilaRequestChangeStrategyFactory;

        /// <summary>
        /// The aquila client service.
        /// </summary>
        private readonly IAquilaClientService aquilaClientService;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<DeleteChannelCommandHandler> logger;

        /// <summary>
        /// The telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// Repository factory.
        /// </summary>
        private readonly IQueryableRepositoryFactory repositoryFactory;

        /// <summary>
        /// Initializes a new instance of the <see cref="DeleteChannelCommandHandler"/> class.
        /// </summary>
        /// <param name="aquilaRequestChangeStrategyFactory">The aquila request change strategy factory.</param>
        /// <param name="aquilaClientService">The aquila client service.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="telemetryService">The telemetry service.</param>
        /// <param name="repositoryFactory">The Repository Factory.</param>
        public DeleteChannelCommandHandler(
            IAquilaRequestChangeStrategyFactory aquilaRequestChangeStrategyFactory,
            IAquilaClientService aquilaClientService,
            ILogger<DeleteChannelCommandHandler> logger,
            ITelemetryService telemetryService,
            IQueryableRepositoryFactory repositoryFactory)
        {
            this.aquilaRequestChangeStrategyFactory = aquilaRequestChangeStrategyFactory;
            this.aquilaClientService = aquilaClientService;
            this.logger = logger;
            this.telemetryService = telemetryService;
            this.repositoryFactory = repositoryFactory;
        }

        /// <summary>
        /// Handles a request.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>
        /// Response from the request.
        /// </returns>
        public async Task<Unit> Handle([NotNull] DeleteChannelCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));
            this.logger.LogInformation("Delete Channel Command Handler Triggered for Channel {Id}", request.ChannelId);

            this.logger.LogInformation("DeleteChannelCommandHandler: Starting deletion process for channel {ChannelId}", request.ChannelId);

            try
            {
                var aquilaChannel = await this.aquilaClientService.GetChannelByIdAsync(request.ChannelId).ConfigureAwait(false);
                var channelState = VideoPlatformUtility.ParseFromEnumMember<AquilaChannelState>(aquilaChannel.State);

                if (channelState != null && channelState.HasValue)
                {
                    var strategy = this.aquilaRequestChangeStrategyFactory.GetRequestChangeStrategy(channelState.Value);
                    this.logger.LogInformation("DeleteChannelCommandHandler: Selected strategy {StrategyType} for channel {ChannelId} in state {ChannelState}",
                            strategy.GetType().Name, request.ChannelId, aquilaChannel.State);

                    int retryCount = 0;
                    while (channelState != AquilaChannelState.Stopped && (retryCount < 10))
                    {
                        this.logger.LogInformation("DeleteChannelCommandHandler: Retrieved channel {ChannelId} with state {ChannelState}", request.ChannelId, aquilaChannel.State);
                        aquilaChannel = await this.aquilaClientService.GetChannelByIdAsync(request.ChannelId).ConfigureAwait(false);
                        strategy = this.aquilaRequestChangeStrategyFactory.GetRequestChangeStrategy(channelState.Value);

                        await Task.Delay(100000);
                        retryCount++;
                        this.logger.LogInformation("DeleteChannelCommandHandler: Waiting for Aquila Channel State to update. Retry count: {retryCount}", retryCount);

                    }

                    if (channelState == AquilaChannelState.Stopped)
                    {
                        this.logger.LogInformation("DeleteChannelCommandHandler: Calling strategy.RequestChangeAsync for channel {ChannelId} to state Deleted", request.ChannelId);
                        await strategy.RequestChangeAsync(AquilaChannelState.Deleted, null, request.ChannelId, request.EventId).ConfigureAwait(false);
                        this.logger.LogInformation("DeleteChannelCommandHandler: Successfully completed strategy.RequestChangeAsync for channel {ChannelId}", request.ChannelId);
                    }
                    else
                    {
                        this.logger.LogCritical("DeleteChannelCommandHandler: Aquila Channel State for {ChannelId} never went to Stopped. Please investigate.", request.ChannelId);
                    }
                }
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "DeleteChannelCommandHandler: Error during deletion process for channel {ChannelId}: {ErrorMessage}",
                    request.ChannelId, ex.Message);
                throw;
            }

            var eventProperties = new Dictionary<string, string>
            {
                { EventData.CorrelationTag, request.CorrelationId },
                { EventData.ChannelIdTag, request.ChannelId },
            };
            this.telemetryService.TrackEvent(request.EventId, EventTypes.AquilaChannelDeleted, eventProperties);

            var videoPlatformChannelRepository = this.repositoryFactory.Resolve<VideoPlatformChannel>();

            var videoPlatformChannel = await videoPlatformChannelRepository.GetItemAsync(request.ChannelId).ConfigureAwait(false);
            videoPlatformChannel.OperationalState = AquilaChannelState.Deleted.ToString();

            await videoPlatformChannelRepository.UpdateItemAsync(videoPlatformChannel).ConfigureAwait(false);

            return Unit.Value;
        }
    }
}
