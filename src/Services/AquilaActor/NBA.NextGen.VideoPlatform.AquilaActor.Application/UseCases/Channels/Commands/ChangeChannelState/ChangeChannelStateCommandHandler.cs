// "//-----------------------------------------------------------------------".
// <copyright file="ChangeChannelStateCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Commands.ChangeChannelState
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Interfaces.Aquila;
    using NBA.NextGen.VideoPlatform.Shared.Application.Aquila.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;

    /// <summary>
    /// Handles state change.
    /// </summary>
    public class ChangeChannelStateCommandHandler : IRequestHandler<ChangeChannelStateCommand, Unit>
    {
        /// <summary>
        /// The aquila request change strategy factory.
        /// </summary>
        private readonly IAquilaRequestChangeStrategyFactory aquilaRequestChangeStrategyFactory;

        /// <summary>
        /// The aquila client service.
        /// </summary>
        private readonly IAquilaClientService aquilaClientService;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<ChangeChannelStateCommandHandler> logger;

        /// <summary>
        /// The event notifier.
        /// </summary>
        private readonly IMessageSender<AquilaUpdatedEvent> eventNotifier;

        /// <summary>
        /// The telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="ChangeChannelStateCommandHandler" /> class.
        /// </summary>
        /// <param name="aquilaRequestChangeStrategyFactory">The aquila request change strategy factory.</param>
        /// <param name="aquilaClientService">The aquila client service.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="telemetryService">The telemetry service.</param>
        /// <param name="eventNotifierProvider">The event notifier provider.</param>
        public ChangeChannelStateCommandHandler(
            IAquilaRequestChangeStrategyFactory aquilaRequestChangeStrategyFactory,
            IAquilaClientService aquilaClientService,
            IMapper mapper,
            ILogger<ChangeChannelStateCommandHandler> logger,
            ITelemetryService telemetryService,
            [NotNull] IMessageSenderFactory eventNotifierProvider)
        {
            eventNotifierProvider.Required(nameof(eventNotifierProvider));
            this.aquilaRequestChangeStrategyFactory = aquilaRequestChangeStrategyFactory;
            this.aquilaClientService = aquilaClientService;
            this.mapper = mapper;
            this.logger = logger;
            this.telemetryService = telemetryService;
            this.eventNotifier = eventNotifierProvider.Resolve<AquilaUpdatedEvent>();
        }

        /// <summary>
        /// Handles a request.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>
        /// Response from the request.
        /// </returns>
        public async Task<Unit> Handle([NotNull] ChangeChannelStateCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));

            try
            {
                this.logger.LogInformation($"{nameof(ChangeChannelStateCommandHandler)} triggered for {nameof(Channel)} {{ChannelId}} and {nameof(ChannelInstance)} {{InstanceId}}", request.ChannelId, request.InstanceId);
                var aquilaChannel = await this.aquilaClientService.GetChannelByIdAsync(request.ChannelId).ConfigureAwait(false);
                var instance = aquilaChannel.Instances.SingleOrDefault(x => x.Id == request.InstanceId);
                // var channelState = VideoPlatformUtility.ParseFromEnumMember<AquilaChannelState>(instance.State);
                this.logger.LogInformation("ChangeChannelStateCommandHandler: Retrieved channel {ChannelId} with state {ChannelState}, Instance state {InstanceState}", request.ChannelId, aquilaChannel.State, instance.State);

                var strategy = this.aquilaRequestChangeStrategyFactory.GetRequestChangeStrategy(instance?.State.ToEnumMember<AquilaChannelState>() ?? aquilaChannel.State.ToEnumMember<AquilaChannelState>());
                this.logger.LogInformation("ChangeChannelStateCommandHandler: Selected strategy {StrategyType} for channel {ChannelId} in state {ChannelState}",
                            strategy.GetType().Name, request.ChannelId, instance.State);
                
                this.logger.LogInformation("ChangeChannelStateCommandHandler: Calling strategy RequestChangeAsync for channel {ChannelId} with state {ChannelState}, Target state {DesiredChannelState}", request.ChannelId, aquilaChannel.State, request.DesiredChannelState);
                await strategy.RequestChangeAsync(request.DesiredChannelState, instance?.Id, request.ChannelId, request.EventId).ConfigureAwait(false);

                var eventProperties = new Dictionary<string, string>
                {
                    { EventData.CorrelationTag, request.CorrelationId },
                    { EventData.ChannelIdTag, request.ChannelId },
                    { EventData.DetailTag, request.DesiredChannelState.ToEnumString() },
                };
                this.telemetryService.TrackEvent(request.EventId, EventTypes.AquilaActorChannelStateUpdated, eventProperties);
            }
            catch (Exception exception)
            {
                if (request.DesiredChannelState == AquilaChannelState.Started)
                {
                    var aquilaUpdatedEvent = this.mapper.Map<AquilaUpdatedEvent>(request);
                    aquilaUpdatedEvent.State = AquilaChannelState.StartRequestError.ToEnumString();
                    await this.eventNotifier.SendAsync(aquilaUpdatedEvent).ConfigureAwait(false);
                }

                this.logger.LogError(exception, $"{nameof(ChangeChannelStateCommandHandler)} failed for {nameof(Channel)} {{ChannelId}} and {nameof(ChannelInstance)} {{InstanceId}}", request.ChannelId, request.InstanceId);

                throw;
            }

            return Unit.Value;
        }
    }
}
