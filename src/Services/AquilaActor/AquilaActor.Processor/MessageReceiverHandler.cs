using System.Linq;
using AutoMapper;
using MediatR;
using MST.Common.Messaging;
using Microsoft.Azure.WebJobs.Extensions.DurableTask;
using NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Queries;
using NBA.NextGen.VideoPlatform.AquilaActor.Domain.Common;
using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
using Newtonsoft.Json;
using NBA.NextGen.Shared.Application.Common;
using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
using NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Commands.SendChannelStateChangeRequestCompleted;
using NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Queries.GetChannelById;
using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities;
using NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Commands;
using NBA.NextGen.Shared.Application.Services;
using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
using NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Commands.SendChannelStateChangeRequestAcknowldgement;
using NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Commands.DeleteChannel;
using NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Commands.CreateChannel;

namespace NBA.NextGen.VideoPlatform.AquilaActor.Processor;

public class MessageReceiverHandler : IMessageHandler
{
    private readonly ILogger<MessageReceiverHandler> _logger;
    private readonly IMediator _mediator;
    private readonly IMapper _mapper;
    private readonly ITelemetryService _telemetryService;
    private const string LogInformationOrchestrationNameMessage = "Triggered {orchestrationName} orchestration for Aquila actor {externalSystemInfrastructureId} and context {context}";

    public MessageReceiverHandler(ILogger<MessageReceiverHandler> logger, IMediator mediator, IMapper mapper, ITelemetryService telemetryService)
    {
        _logger = logger;
        _mediator = mediator;
        _mapper = mapper;
        _telemetryService = telemetryService;
    }

    public async Task ProcessMessage(ReceivedMessage receivedMessage)
    {
        var body = receivedMessage.Content;
        var message = JsonConvert.DeserializeObject<InfrastructureStateChangeRequest>(body ?? "");

        _logger.LogInformation("ReceiveMessage function triggered for {actorId} with id {Id} ", message?.ActorId, message?.ExternalSystemInfrastructureId ?? "");

        var orchQuery = new GetOrchestratorQuery()
        {
            WorkflowId = message?.WorkflowId
        };

        var orchestrationName = await _mediator.Send(orchQuery);
        switch (orchestrationName)
        {
            case OrchestrationNames.StartChannelRequestWorkflowOrchestration:
                await StartAsync(body ?? "").ConfigureAwait(false);
                break;
            case OrchestrationNames.StopAndDeleteChannelRequestWorkflowOrchestration:
                await StopAndDeleteAsync(body ?? "").ConfigureAwait(false);
                break;
            case OrchestrationNames.CreateChannelRequestWorkflowOrchestration:
                await CreateChannelsAsync(body ?? "").ConfigureAwait(false);
                break;
            default:
                _logger.LogError("Workflow not valid for AquilaActor");
                break;
        }

        _logger.LogInformation($"Orchestrator: {orchestrationName}");
    }

    public async Task StartAsync(string message)
    {
        var infrastructureStateChangeRequest = JsonConvert.DeserializeObject<InfrastructureStateChangeRequest<ChannelStateChangeInfo>>(message);
        _logger.LogInformation(
            LogInformationOrchestrationNameMessage,
            OrchestrationNames.StartChannelRequestWorkflowOrchestration,
            infrastructureStateChangeRequest?.ExternalSystemInfrastructureId,
            JsonConvert.SerializeObject(infrastructureStateChangeRequest));
        _logger.LogInformation(
            "Triggered durable orchestration for aquila actor for channel {ChannelId}",
            infrastructureStateChangeRequest?.ActorSpecificDetail.Data.ChannelId);

        var ackCommand = _mapper.Map<SendChannelStateChangeRequestAcknowldgementCommand>(infrastructureStateChangeRequest);
        await _mediator.Send(ackCommand);

        var changeAppliedNotificationCommand = _mapper.Map<SendChannelStateChangeRequestCompletedCommand>(infrastructureStateChangeRequest);
        try
        {
            var invalidStarteableStates = new AquilaChannelState[]
            {
                    AquilaChannelState.CreationError,
                    AquilaChannelState.StartRequested,
                    AquilaChannelState.Starting,
                    AquilaChannelState.Started,
            };
            var getChannelByIdQuery = _mapper.Map<GetChannelByIdQuery>(infrastructureStateChangeRequest);
            var channel = await _mediator.Send(getChannelByIdQuery);
            var starteableInstanceIds = channel.Instances.Where(x => !invalidStarteableStates.Contains(x.State.ToEnumMember<AquilaChannelState>())).Select(x => x.Id);

            if (starteableInstanceIds.Any())
            {
                var tasks = new List<Task>();
                foreach (var instanceId in starteableInstanceIds)
                {
                    _logger.LogInformation($"Initializing starting process for {nameof(Channel)} {{ChannelId}} and {nameof(ChannelInstance)} {{InstanceId}}", channel.Id, instanceId);
                    var changeChannelStateCommand = _mapper.Map<ChangeChannelStateCommand>(infrastructureStateChangeRequest);
                    changeChannelStateCommand.DesiredChannelState = AquilaChannelState.Started;
                    changeChannelStateCommand.InstanceId = instanceId;

                    tasks.Add(_mediator.Send(changeChannelStateCommand));

                    _logger.LogInformation($"Requested starting process for {nameof(Channel)} {{ChannelId}} and {nameof(ChannelInstance)} {{InstanceId}}", channel.Id, instanceId);
                }
                await Task.WhenAll(tasks);
            }
            _telemetryService.TrackEvent(infrastructureStateChangeRequest?.ActorSpecificDetail.Data.EventId, EventTypes.AquilaActorChannelStarted, EventData.CorrelationTag);
            changeAppliedNotificationCommand.State = InfrastructureState.Started;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                "{functionName} failed with the exception of type: {type} with message: {message}",
                OrchestrationNames.StartChannelRequestWorkflowOrchestration,
                ex.InnerException?.GetType().Name,
                ex.InnerException?.Message);
            changeAppliedNotificationCommand.State = InfrastructureState.Failed;
            changeAppliedNotificationCommand.ErrorMessage = ex.Message;
        }

        await _mediator.Send(changeAppliedNotificationCommand);

        await Task.CompletedTask.ConfigureAwait(true);
    }

    public async Task StopAndDeleteAsync(string message)
    {
        var infrastructureStateChangeRequest = JsonConvert.DeserializeObject<InfrastructureStateChangeRequest<ChannelStateChangeInfo>>(message);
        _logger.LogInformation("Triggered Durable Orchestration for Aquila Actor for channel {ChannelId}", infrastructureStateChangeRequest?.ActorSpecificDetail.Data.ChannelId);

        var ackCommand = _mapper.Map<SendChannelStateChangeRequestAcknowldgementCommand>(infrastructureStateChangeRequest);
        await _mediator.Send(ackCommand);

        var changeAppliedNotificationCommand = _mapper.Map<SendChannelStateChangeRequestCompletedCommand>(infrastructureStateChangeRequest);

        try
        {
            var getChannelByIdQuery = _mapper.Map<GetChannelByIdQuery>(infrastructureStateChangeRequest);
            var channel = await _mediator.Send(getChannelByIdQuery);
            var stoppeableInstanceIds = channel.Instances.Where(x =>
                x.State.ToEnumMember<AquilaChannelState>() != AquilaChannelState.Stopped &&
                x.State.ToEnumMember<AquilaChannelState>() != AquilaChannelState.Stopping).Select(x => x.Id);

            _logger.LogInformation($"Values: stoppeableInstanceIds {{InstanceId}}", String.Join(", ", stoppeableInstanceIds));

            if (stoppeableInstanceIds.Any())
            {
                foreach (var instanceId in stoppeableInstanceIds)
                {
                    _logger.LogInformation($"Initializing stopping process for {nameof(Channel)} {{ChannelId}} and {nameof(ChannelInstance)} {{InstanceId}}", channel.Id, instanceId);
                    var applyChangeCommand = _mapper.Map<ChangeChannelStateCommand>(infrastructureStateChangeRequest);
                    applyChangeCommand.DesiredChannelState = AquilaChannelState.Stopped;
                    applyChangeCommand.InstanceId = instanceId;
                    await _mediator.Send(applyChangeCommand);
                    _logger.LogInformation($"Completed stopping process for {nameof(Channel)} {{ChannelId}} and {nameof(ChannelInstance)} {{InstanceId}}", channel.Id, instanceId);
                }

            }

            _logger.LogInformation($"Initializing deleting process for {nameof(Channel)} {{ChannelId}}", channel.Id);
            _telemetryService.TrackEvent(infrastructureStateChangeRequest?.ActorSpecificDetail.Data.EventId, EventTypes.AquilaActorChannelStateSetToDeleted, EventData.CorrelationTag);
            var deleteCommand = _mapper.Map<DeleteChannelCommand>(infrastructureStateChangeRequest);
            await _mediator.Send(deleteCommand);
            _logger.LogInformation($"Completed deleting process for {nameof(Channel)} {{ChannelId}}", channel.Id);
            changeAppliedNotificationCommand.State = InfrastructureState.Stopped;


            _logger.LogInformation($"Attempted to stop and delete every available Stoppable channel instance.");
        }
        catch (Exception ex)
        {
            _logger.LogError(
                "{functionName} failed with the exception of type: {type} with message: {message}",
                OrchestrationNames.StopAndDeleteChannelRequestWorkflowOrchestration,
                ex.InnerException?.GetType().Name,
                ex.InnerException?.Message);
            changeAppliedNotificationCommand.State = InfrastructureState.Failed;
            changeAppliedNotificationCommand.ErrorMessage = ex.Message;
        }

        await _mediator.Send(changeAppliedNotificationCommand);
        await Task.CompletedTask.ConfigureAwait(true);
    }

    public async Task CreateChannelsAsync(string message)
    {
        var infrastructureStateChangeRequest = JsonConvert.DeserializeObject<InfrastructureStateChangeRequest<IList<ChannelCreationInfo>>>(message);
        _logger.LogInformation(
            LogInformationOrchestrationNameMessage,
            OrchestrationNames.StartChannelRequestWorkflowOrchestration,
            infrastructureStateChangeRequest?.ExternalSystemInfrastructureId,
            JsonConvert.SerializeObject(infrastructureStateChangeRequest));
        _logger.LogInformation("Triggered durable orchestration for aquila actor for channels {ChannelId}", string.Join(", ", infrastructureStateChangeRequest?.ActorSpecificDetail.Data.Select(x => x.ChannelId) ?? new List<string>()));

        var changeAppliedNotificationCommand = _mapper.Map<SendChannelStateChangeRequestCompletedCommand>(infrastructureStateChangeRequest);
        changeAppliedNotificationCommand.EventId = infrastructureStateChangeRequest?.ActorSpecificDetail.Data.FirstOrDefault()?.EventId;

        try
        {
            var ackCommand = _mapper.Map<SendChannelStateChangeRequestAcknowldgementCommand>(infrastructureStateChangeRequest);
            ackCommand.EventId = infrastructureStateChangeRequest?.ActorSpecificDetail.Data.FirstOrDefault()?.EventId;
            await _mediator.Send(ackCommand);

            var createChannelCommand = _mapper.Map<CreateChannelCommand>(infrastructureStateChangeRequest);
            createChannelCommand.EventId = infrastructureStateChangeRequest?.ActorSpecificDetail.Data.FirstOrDefault()?.EventId;
            await _mediator.Send(createChannelCommand);
            changeAppliedNotificationCommand.State = InfrastructureState.Configured;
        }
        catch (Exception ex)
        {
            _logger.LogError("{functionName} failed with the exception of type: {type} with message: {message}", OrchestrationNames.CreateChannelRequestWorkflowOrchestration, ex.InnerException?.GetType().Name, ex.InnerException?.Message);
            changeAppliedNotificationCommand.State = InfrastructureState.Failed;
            changeAppliedNotificationCommand.ErrorMessage = ex.Message;
        }

        await _mediator.Send(changeAppliedNotificationCommand);

        await Task.CompletedTask.ConfigureAwait(true);
    }

    public Task ProcessError(Exception exception)
    {
        _logger.LogError($"AquilaMessageReceiverEventHandler Queue Error: {exception.Message}");
        return Task.CompletedTask;
    }
}
